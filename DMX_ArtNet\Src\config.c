#include "config.h"
#include "dmx.h"
#include "eth/dhcp_server.h"
#include "flash_ee.h"
#include "lwip/apps/mdns.h"
#include "lwip/autoip.h"
#include "lwip/dhcp.h"
#include "lwip/ip_addr.h"
#include "lwip/netif.h"
#include "usart.h"
#include "stm32g4xx.h"

static struct netif *netif;
static CONFIG activeConfig;

void Config_Init(struct netif *net) {
    netif = net;

    activeConfig = Config_GetDefault();
    EE_ReadConfig(&activeConfig);
    Config_ApplyNetwork();
    Config_ApplyCrmxBControl();
    Config_ApplyTimoTwoPins();
}

struct netif *Config_GetNetif() {
    return netif;
}

CONFIG *Config_GetActive() {
    return &activeConfig;
}

void Config_Reset() {
    activeConfig = Config_GetDefault();
    EE_ClearConfig();
    Config_ApplyArtNet();
    Config_ApplyNetwork();
}

void Config_DhcpServer(char enable, ip_addr_t host, ip_addr_t client, ip_addr_t subnet) {
    activeConfig.DhcpServerEnable = enable;
    activeConfig.DhcpServerSelf = host;
    activeConfig.DhcpServerClient = client;
    activeConfig.DhcpServerSubnet = subnet;
}

void Config_SetMode(CONFIG_IP_MODE mode) {
    activeConfig.Mode = mode;
}

void Config_SetIp(const unsigned char *ip) {
    IP4_ADDR(&activeConfig.StaticIp, ip[0], ip[1], ip[2], ip[3]);
}

void Config_SetGateway(const unsigned char *gw) {
    IP4_ADDR(&activeConfig.StaticGateway, gw[0], gw[1], gw[2], gw[3]);
}

void Config_SetNetmask(const unsigned char *net) {
    IP4_ADDR(&activeConfig.StaticSubnet, net[0], net[1], net[2], net[3]);
}

void Config_ApplyNetwork() {
    dhcp_stop(netif);
    autoip_stop(netif);

    DhcpServer_Configure(&activeConfig.DhcpServerClient, &activeConfig.DhcpServerSubnet);

    if (activeConfig.DhcpServerEnable) {
        DhcpServer_Start(netif);
    } else {
        DhcpServer_Stop(netif);
    }

    switch (activeConfig.Mode) {
    case CONFIGIP_Auto:
        netif_set_addr(netif, IP4_ADDR_ANY, IP4_ADDR_ANY, IP4_ADDR_ANY);
        autoip_start(netif);
        break;
    case CONFIGIP_Static:
        netif_set_addr(netif, &activeConfig.StaticIp, &activeConfig.StaticSubnet, &activeConfig.StaticGateway);
        break;
    case CONFIGIP_DHCP:
        if (activeConfig.DhcpServerEnable) {
            netif_set_addr(netif, &activeConfig.DhcpServerSelf, &activeConfig.DhcpServerSubnet, &activeConfig.DhcpServerSelf);
        } else {
            netif_set_addr(netif, IP4_ADDR_ANY, IP4_ADDR_ANY, IP4_ADDR_ANY);
            dhcp_start(netif);
        }
        break;
    }

    mdns_resp_netif_settings_changed(netif);
}

void Config_ApplyCrmxBControl() {
    // PA15 controls CRMX B module enable/disable
    // When enable1 == 1 -> PA15 = HIGH (3.3V)
    // When enable1 == 0 -> PA15 = LOW (GND)

    if (activeConfig.ArtNet[1].PortEnabled) {
        GPIOA->BSRR = GPIO_BSRR_BS15;  // Set PA15 HIGH
    } else {
        GPIOA->BSRR = GPIO_BSRR_BR15;  // Set PA15 LOW
    }
}

void Config_ApplyTimoTwoPins() {
    // PB13 controls TimoTwo FX Module A (port 0)
    // PB12 controls TimoTwo FX Module B (port 1)
    // When output == 1 (Input mode) -> PIN = HIGH
    // When output == 0 (Output mode) -> PIN = LOW

    // Control PB13 (Module A / Port 0)
    if (activeConfig.ArtNet[0].PortDirection == ARTNET_INPUT) {
        GPIOB->BSRR = GPIO_BSRR_BS13;  // Set PB13 HIGH
    } else {
        GPIOB->BSRR = GPIO_BSRR_BR13;  // Set PB13 LOW
    }

    // Control PB12 (Module B / Port 1)
    // NOTE: PB12 is also monitored by PA15 edge detection for additional safety
    if (activeConfig.ArtNet[1].PortDirection == ARTNET_INPUT) {
        GPIOB->BSRR = GPIO_BSRR_BS12;  // Set PB12 HIGH
    } else {
        GPIOB->BSRR = GPIO_BSRR_BR12;  // Set PB12 LOW
    }
}

void Config_ApplyArtNet() {
    for (int i = 0; i < 2; i++) {
        DMX_SetInDisabled(i, activeConfig.ArtNet[i].PortFlags & PORT_FLAG_INDISABLED);
        DMX_SetSingleMode(i, activeConfig.ArtNet[i].PortFlags & PORT_FLAG_SINGLE);

        USART_ChangeDirection(i, activeConfig.ArtNet[i].PortDirection);
    }

    // Update CRMX B control pin (PA15)
    Config_ApplyCrmxBControl();

    // Update TimoTwo FX module control pins
    Config_ApplyTimoTwoPins();
}

void Config_Store() {
    EE_WriteConfig(&activeConfig);
}

CONFIG Config_GetDefault() {
    CONFIG cfg = {.Mode = CONFIGIP_DHCP,
                  .DhcpServerEnable = 1};

    // Unified IP settings: both DHCP Server and Static IP use same default values
    IP4_ADDR(&cfg.DhcpServerSelf, 10, 0, 0, 1);     // Steady Node IP
    IP4_ADDR(&cfg.DhcpServerClient, 10, 0, 0, 2);   // iPad IP (DHCP client)
    IP4_ADDR(&cfg.DhcpServerSubnet, 255, 0, 0, 0);  // Net Mask

    // Static IP now uses same unified values as DHCP Server
    IP4_ADDR(&cfg.StaticIp, 10, 0, 0, 1);           // Steady Node IP (unified)
    IP4_ADDR(&cfg.StaticGateway, 10, 0, 0, 1);      // Gateway (unified)
    IP4_ADDR(&cfg.StaticSubnet, 255, 0, 0, 0);      // Net Mask (unified)

    for (int i = 0; i < 2; i++) {
        sprintf(cfg.ArtNet[i].ShortName, "ArtNet-Port %i", i);
        sprintf(cfg.ArtNet[i].LongName, "Steady Node Port %i", i);

        cfg.ArtNet[i].Network = ARTNET_NET;
        cfg.ArtNet[i].Subnet = ARTNET_SUB;

        cfg.ArtNet[i].Universe = ARTNET_UNI + i;

        cfg.ArtNet[i].FailoverMode = ArtFail_Hold;
        cfg.ArtNet[i].PortDirection = ARTNET_OUTPUT;
        cfg.ArtNet[i].PortEnabled = 1;
    }

    return cfg;
}
