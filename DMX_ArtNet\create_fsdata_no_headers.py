#!/usr/bin/env python3
"""
<PERSON>ript to create fsdata.c without HTTP headers for lwIP with LWIP_HTTPD_DYNAMIC_HEADERS=1
This prevents the HTTP headers from being displayed as content in the browser.
"""

import os
import sys

def file_to_c_array(filename, varname):
    """Convert a file to C array data without HTTP headers"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    # Create the C array
    c_data = []
    c_data.append(f"static const unsigned char data_{varname}[] = {{")
    c_data.append(f"\t/* /{filename} */")
    
    # Add filename as null-terminated string
    filename_bytes = filename.encode('utf-8') + b'\x00'
    line = "\t"
    for i, byte in enumerate(filename_bytes):
        if i > 0 and i % 12 == 0:
            c_data.append(line)
            line = "\t"
        line += f"0x{byte:02x}, "
    if line.strip() != "":
        c_data.append(line.rstrip(', ') + ",")
    
    # Add file content (without HTTP headers)
    line = "\t"
    for i, byte in enumerate(data):
        if i > 0 and i % 12 == 0:
            c_data.append(line)
            line = "\t"
        line += f"0x{byte:02x}, "
    
    if line.strip() != "":
        c_data.append(line.rstrip(', '))
    
    c_data.append("};")
    c_data.append("")
    
    return '\n'.join(c_data), len(filename_bytes)

def create_fsdata():
    """Create fsdata.c without HTTP headers"""
    
    # Files to include
    files = [
        ('device.html', 'device_html'),
        ('index.html', 'index_html'),
        ('style.css', 'style_css'),
        ('min/device.html', 'min_device_html'),
        ('min/index.html', 'min_index_html'),
        ('min/style.css', 'min_style_css')
    ]
    
    # Check if files exist
    for filename, _ in files:
        if not os.path.exists(filename):
            print(f"Warning: {filename} not found, skipping...")
    
    # Generate C code
    c_code = []
    c_code.append('#include "lwip/apps/fs.h"')
    c_code.append('#include "lwip/def.h"')
    c_code.append('')
    c_code.append('#define file_NULL (struct fsdata_file *) NULL')
    c_code.append('')
    c_code.append('#ifndef FS_FILE_FLAGS_HEADER_INCLUDED')
    c_code.append('#define FS_FILE_FLAGS_HEADER_INCLUDED 1')
    c_code.append('#endif')
    c_code.append('#ifndef FS_FILE_FLAGS_HEADER_PERSISTENT')
    c_code.append('#define FS_FILE_FLAGS_HEADER_PERSISTENT 0')
    c_code.append('#endif')
    c_code.append('')
    
    # Generate data arrays
    file_structs = []
    prev_file = "file_NULL"
    
    for filename, varname in files:
        if os.path.exists(filename):
            print(f"Processing {filename}...")
            array_code, filename_len = file_to_c_array(filename, varname)
            c_code.append(array_code)
            
            # Create file structure
            struct_code = f"const struct fsdata_file file_{varname}[] = {{{{" + \
                         f"{prev_file}, data_{varname}, data_{varname} + {filename_len}, " + \
                         f"sizeof(data_{varname}) - {filename_len}, 0}}}};"
            file_structs.append(struct_code)
            prev_file = f"file_{varname}"
    
    # Add file structures
    c_code.extend(file_structs)
    c_code.append('')
    c_code.append(f'#define FS_ROOT {prev_file}')
    c_code.append(f'#define FS_NUMFILES {len([f for f, _ in files if os.path.exists(f)])}')
    c_code.append('')
    
    # Write to file
    with open('fsdata.c', 'w') as f:
        f.write('\n'.join(c_code))
    
    print("fsdata.c created successfully without HTTP headers!")

if __name__ == "__main__":
    create_fsdata()
